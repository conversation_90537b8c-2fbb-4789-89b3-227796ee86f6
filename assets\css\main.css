@import url("https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,600,700");
html, body, div, span, applet, object,
iframe, h1, h2, h3, h4, h5, h6, p, blockquote,
pre, a, abbr, acronym, address, big, cite,
code, del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var, b,
u, i, center, dl, dt, dd, ol, ul, li, fieldset,
form, label, legend, table, caption, tbody,
tfoot, thead, tr, th, td, article, aside,
canvas, details, embed, figure, figcaption,
footer, header, hgroup, menu, nav, output, ruby,
section, summary, time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline; }

article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block; }

body {
  line-height: 1; }

ol, ul {
  list-style: none; }

blockquote, q {
  quotes: none; }
  blockquote:before, blockquote:after, q:before, q:after {
    content: '';
    content: none; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

body {
  -webkit-text-size-adjust: none; }

mark {
  background-color: transparent;
  color: inherit; }

input::-moz-focus-inner {
  border: 0;
  padding: 0; }

input, select, textarea {
  -moz-appearance: none;
  -webkit-appearance: none;
  -ms-appearance: none;
  appearance: none; }

@font-face {
  font-family: 'h5u';
  src: url("fonts/h5u.eot");
  src: url("fonts/h5u.eot?#iefix") format("embedded-opentype"), url("fonts/h5u.woff") format("woff"), url("fonts/h5u.ttf") format("truetype"), url("fonts/h5u.svg#h5u") format("svg");
  font-weight: normal;
  font-style: normal; }

/* Basic */
@-ms-viewport {
  width: device-width; }

body {
  -ms-overflow-style: scrollbar; }

@media screen and (max-width: 736px) {
  html, body {
    min-width: 320px; } }

html {
  box-sizing: border-box; }

*, *:before, *:after {
  box-sizing: inherit; }

/* Container */
.container {
  margin: 0 auto;
  max-width: 1200px;
  width: 100%; }
  @media screen and (max-width: 1280px) {
    .container {
      max-width: 1000px; } }
  @media screen and (max-width: 1140px) {
    .container {
      width: 95%; } }

/* Main */
body {
  background: #fff; }
  body.is-loading *, body.is-loading *:before, body.is-loading *:after {
    -moz-transition: none !important;
    -webkit-transition: none !important;
    -ms-transition: none !important;
    transition: none !important;
    -moz-animation: none !important;
    -webkit-animation: none !important;
    -ms-animation: none !important;
    animation: none !important; }

body, input, textarea, select {
  font-family: 'Source Sans Pro', Arial, sans-serif;
  font-size: 13pt;
  line-height: 1.5em;
  color: #676d79;
  font-weight: 300; }

b, strong {
  font-weight: 600; }

a {
  text-decoration: none;
  border-bottom: dotted 1px #d0d6e2;
  color: inherit;
  -moz-transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  -webkit-transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  -ms-transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out, background-color 0.2s ease-in-out; }
  a:hover {
    color: #e7746f;
    border-color: #e7746f; }

hr {
  border: 0;
  box-shadow: inset 0 1px 0 0 #DEE0E3;
  margin: 2em 0 0 0;
  padding: 2em 0 0 0; }

input, textarea, select {
  -webkit-appearance: none;
  display: block;
  width: 100%;
  height: 2.75em;
  line-height: 2.7em;
  padding: 0 1em;
  border-radius: 4px;
  text-decoration: none;
  border: solid 1px #dee0e3;
  background-color: #F4F5F7;
  outline: 0; }
  input:focus, textarea:focus, select:focus {
    border-color: #E7746F;
    box-shadow: 0 0 0 1px #E7746F; }

form {
  display: -moz-flex;
  display: -webkit-flex;
  display: -ms-flex;
  display: flex;
  width: 100%; }
  form > * {
    margin-left: 1em;
    -ms-flex: 0 1 auto; }
  form > :first-child {
    margin-left: 0; }

.button {
  display: inline-block;
  height: 2.75em;
  line-height: 2.7em;
  padding: 0 1.5em 0 1.5em;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  border: solid 1px #dee0e3;
  white-space: nowrap; }
  .button:hover {
    border-color: #e7746f;
    color: #e7746f; }
  .button.alt {
    background: #676d79;
    border-color: #676d79;
    color: #fff; }
    .button.alt.on, .button.alt:hover {
      background: #e7746f;
      border-color: #e7746f; }
  .button.twitter.on, .button.twitter:hover {
    background: #00acee;
    border-color: #00acee;
    color: #fff; }
  .button em {
    font-weight: 300; }
  .button.icon {
    padding: 0 1.2em 0 1em; }
    .button.icon:before {
      position: relative;
      top: 0.15em;
      margin-right: 0.4em; }
    .button.icon.solo {
      padding: 0;
      width: 3em;
      text-align: center;
      position: relative; }
      .button.icon.solo:before {
        margin: 0; }
      .button.icon.solo .label {
        position: absolute;
        left: 0;
        top: 0;
        text-indent: 100%;
        overflow-x: hidden; }

ul.share {
  display: inline-block;
  height: 2.75em;
  line-height: 2.75em;
  padding: 0 1em 0 1em;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  border: solid 1px #dee0e3;
  color: #676d79;
  overflow: hidden; }
  ul.share li {
    position: relative;
    display: inline-block;
    height: 20px;
    line-height: 20px;
    margin-right: 10px;
    margin-top: -3px;
    vertical-align: middle; }
    ul.share li.facebook {
      width: 56px; }
    ul.share li.twitter {
      width: 60px;
      margin-right: 0; }

ul.selector {
  display: inline-block;
  height: 2.75em;
  line-height: 2.7em;
  padding: 0;
  text-decoration: none;
  font-weight: 600;
  color: inherit;
  overflow: hidden; }
  ul.selector li {
    position: relative;
    display: block;
    float: left;
    overflow: hidden;
    padding: 0 1em 0 1em;
    cursor: pointer;
    -moz-transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    border-style: solid;
    border-color: #dee0e3;
    height: 2.75em;
    border-top-width: 1px;
    border-bottom-width: 1px; }
    ul.selector li:first-child {
      border-radius: 4px 0 0 4px;
      border-left-width: 1px; }
    ul.selector li:last-child {
      border-radius: 0 4px 4px 0;
      border-right-width: 1px; }
    ul.selector li:hover {
      background: #f2f4f7; }
    ul.selector li.active {
      background: #676d79;
      color: #fff;
      border-color: #676d79; }

ul.menu li {
  display: inline-block; }
  ul.menu li:before {
    content: '\2022';
    padding: 0 0.75em 0 0.75em;
    color: #dee0e3; }
  ul.menu li:first-child:before {
    display: none; }

.actions .button {
  margin-left: 0.75em; }
  .actions .button:first-child {
    margin-left: 0 !important; }

ul.checklist li {
  margin: 0.5em 0 0 0; }
  ul.checklist li:first-child {
    margin-top: 0; }
  ul.checklist li:before {
    color: #e7746f;
    margin-right: 0.75em; }

article.modal {
  border: solid 1px #dee0e3;
  padding: 3em;
  text-align: center;
  font-size: 1.25em;
  line-height: 1.75em;
  border-radius: 4px;
  max-width: 640px; }
  article.modal p, article.modal ul {
    margin: 0 0 1em 0; }
  article.modal > :last-child {
    margin-bottom: 0; }
  article.modal h3 {
    font-size: 1em;
    line-height: 1.35em;
    font-weight: 700;
    letter-spacing: -0.5px;
    margin: 0 0 1em 0; }

.license-button {
  display: block;
  border: 0;
  margin: 0 0 1em 0;
  outline: 0; }

/* Icons */
/*
		Icons pulled from the following (via icomoon.io/app):

			Icon Set:	Font Awesome -- http://fortawesome.github.com/Font-Awesome/
			License:	SIL -- http://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL

			Icon Set:	IcoMoon - Free -- http://keyamoon.com/icomoon/
			License:	CC BY 3.0 -- http://creativecommons.org/licenses/by/3.0/

			Icon Set:	Broccolidry -- http://dribbble.com/shots/587469-Free-16px-Broccolidryiconsaniconsetitisfullof-icons
			License:	Aribitrary -- http://licence.visualidiot.com/
	*/
.icon:before {
  font-family: 'h5u';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased; }

.icon-responsive:before {
  content: '\e001'; }

.icon-code:before {
  content: '\e002'; }

.icon-cog:before {
  content: '\e003'; }

.icon-gift:before {
  content: '\e004'; }

.icon-twitter:before {
  content: '\e005'; }

.icon-plus:before {
  content: '\e006'; }

.icon-check:before {
  content: '\e007'; }

.icon-popout:before {
  content: '\e008';
  left: 0.1em; }

/* Header */
#header {
  position: relative;
  margin: 6em 0 2em 0;
  padding-bottom: 8em;
  cursor: default; }
  #header .logo a {
    display: inline-block;
    background: #e7746f;
    color: #fff;
    border-radius: 4px;
    padding: 0.55em 0.6em;
    text-decoration: none;
    font-weight: 700;
    font-size: 2.3em;
    margin: 0 0 0.5em 0;
    border: 0; }
    #header .logo a span {
      font-weight: 300; }
  #header .about {
    font-size: 2.5em;
    line-height: 1.35em;
    font-weight: 200;
    letter-spacing: -1.5px; }
    #header .about strong {
      font-weight: 700; }
  #header .features {
    position: absolute;
    right: 0;
    top: 0; }
    #header .features li {
      position: relative;
      text-align: center;
      display: inline-block;
      width: 9em;
      font-size: 1.25em;
      letter-spacing: -0.5px; }
      #header .features li:after {
        font-family: 'h5u';
        content: '\e006';
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        position: absolute;
        width: 2em;
        height: 2em;
        line-height: 2em;
        top: 1em;
        left: -1.05em;
        color: #dee0e3;
        font-size: 1.5em; }
      #header .features li:before {
        display: block;
        margin: 0 auto 0.35em auto;
        border-radius: 100%;
        background: transparent;
        font-size: 2.75em;
        width: 2.1em;
        height: 2.1em;
        line-height: 2.1em;
        text-align: center;
        box-shadow: 0 0 0 1px #dee0e3;
        cursor: default;
        -moz-transition: all 0.2s ease-in-out;
        -webkit-transition: all 0.2s ease-in-out;
        -ms-transition: all 0.2s ease-in-out;
        transition: all 0.2s ease-in-out; }
      #header .features li:first-child:after {
        display: none; }
  #header.alt {
    text-align: center;
    margin: 8em 0 4em 0;
    padding: 0; }
    #header.alt .logo a {
      margin: 0 0 0.75em 0; }
    #header.alt h2 {
      font-size: 2.5em;
      line-height: 1.35em;
      font-weight: 700;
      letter-spacing: -1px; }

/* Bar */
#bar {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2.75em; }
  #bar .share {
    position: absolute;
    left: 0;
    top: 0; }
  #bar .notify {
    position: absolute;
    right: 0;
    top: 0; }
    #bar .notify .button {
      margin-left: 1em; }
  #bar.docked {
    position: fixed;
    left: 0;
    bottom: auto;
    top: -3.75em;
    background: #fff;
    background: rgba(255, 255, 255, 0.975);
    z-index: 10000;
    box-shadow: 0 0 1px 0 rgba(16, 16, 32, 0.25), 0 0 2px 0 rgba(0, 0, 0, 0.15);
    height: 3.75em;
    -moz-transition: top 0.25s ease-in-out;
    -webkit-transition: top 0.25s ease-in-out;
    -ms-transition: top 0.25s ease-in-out;
    transition: top 0.25s ease-in-out;
    font-size: 0.9em; }
    #bar.docked .share {
      top: 0.5em;
      left: 0.5em; }
    #bar.docked .notify {
      top: 0.5em;
      right: 0.5em; }
    #bar.docked.active {
      top: 0; }

/* Items */
#items {
  position: relative; }
  #items article {
    position: relative;
    overflow: hidden;
    margin: 2em 0 0 0;
    border-radius: 6px; }
    #items article .image {
      display: block;
      width: 100%;
      border: 0;
      position: relative;
      overflow: hidden; }
      #items article .image:before {
        content: '';
        display: block;
        padding-top: 30.416666666666666666666666666667%; }
      #items article .image img {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: block;
        width: 100%;
        outline: 0; }
      #items article .image .placeholder {
        -moz-transition: opacity 0.35s ease-in-out;
        -webkit-transition: opacity 0.35s ease-in-out;
        -ms-transition: opacity 0.35s ease-in-out;
        transition: opacity 0.35s ease-in-out;
        opacity: 0; }
      #items article .image.is-cached .placeholder {
        -moz-transition: none !important;
        -webkit-transition: none !important;
        -ms-transition: none !important;
        transition: none !important;
        opacity: 1.0; }
      #items article .image.is-loaded .placeholder {
        opacity: 1.0; }
    #items article header {
      position: absolute;
      bottom: 33%;
      left: 63%;
      cursor: default; }
      #items article header h2 {
        font-size: 2.35em;
        font-weight: 600;
        margin: 0 0 0.75em 0;
        letter-spacing: -1.5px; }
      #items article header .button {
        height: 3em;
        line-height: 2.95em;
        padding: 0 1.5em 0 1.5em; }

/* Footer */
#footer {
  position: relative;
  padding: 5em 0 6em 0;
  text-align: center; }
  #footer .signup {
    width: 30em;
    margin: 2em auto;
    max-width: 100%; }
  #footer .copyright {
    display: block;
    margin-top: 2em; }
    #footer .copyright br {
      display: none; }

/* Demo */
body#demo {
  background: #fafbfd; }

#demo-iframe-wrapper {
  position: fixed;
  background: #dee0e3;
  top: 3.375em;
  left: 0;
  width: 100%;
  height: calc(100% - 3.375em);
  opacity: 1.0;
  border-radius: 10px;
  -moz-transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out; }

body.overlap #demo-iframe-wrapper {
  top: 0;
  height: 100%; }

#demo-iframe-wrapper iframe {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -moz-transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
  overflow-x: hidden; }

#demo-iframe-wrapper.loading iframe {
  opacity: 0; }

#demo-iframe-wrapper.framed {
  box-shadow: 0 0 0 29px #fff, 0 0 0 30px #dee0e3; }
  #demo-iframe-wrapper.framed iframe {
    box-shadow: 0 0 0 1px #dee0e3; }

#demo-header {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  background: #fff;
  background: rgba(255, 255, 255, 0.95);
  z-index: 10000;
  box-shadow: 0 0 1px 0 rgba(16, 16, 32, 0.25), 0 0 2px 0 rgba(0, 0, 0, 0.15);
  height: 3.75em;
  -moz-transition: top 0.25s ease-in-out;
  -webkit-transition: top 0.25s ease-in-out;
  -ms-transition: top 0.25s ease-in-out;
  transition: top 0.25s ease-in-out;
  font-size: 0.9em;
  z-index: 10000;
  cursor: default; }
  #demo-header .button.alt2 {
    outline: 0; }
  #demo-header .left {
    position: absolute;
    top: 0.5em;
    left: 0.5em;
    white-space: nowrap;
    overflow: hidden; }
    #demo-header .left .selector {
      float: left; }
    #demo-header .left .actions {
      float: left;
      margin-left: 0.5em; }
  #demo-header .right {
    position: absolute;
    top: 0.5em;
    right: 0.5em;
    white-space: nowrap;
    overflow: hidden; }
    #demo-header .right .actions {
      float: left;
      margin-left: 0.5em; }
      #demo-header .right .actions a {
        margin-left: 0.5em; }
  #demo-header h1 {
    letter-spacing: -0.5px;
    font-weight: 600;
    float: left;
    height: 2.75em;
    line-height: 2.75em;
    margin: 0 1.25em 0 0.5em;
    display: inline-block; }
    #demo-header h1 span {
      font-size: 1.25em; }
  #demo-header .share {
    float: left; }

body.dark #demo-header {
  background: #272d39;
  background: rgba(55, 61, 73, 0.975);
  color: #fff;
  box-shadow: none; }
  body.dark #demo-header .selector li, body.dark #demo-header .share {
    border-color: #575d69; }
  body.dark #demo-header .button.alt2 {
    border-color: #575d69; }
    body.dark #demo-header .button.alt2:hover {
      border-color: #e7746f; }
  body.dark #demo-header .selector li:hover {
    background: #474d59; }
  body.dark #demo-header .selector li.active {
    border-color: #676d79;
    background: #676d79; }

/* Dialog */
#dialog {
  -moz-align-items: center;
  -webkit-align-items: center;
  -ms-align-items: center;
  align-items: center;
  display: -moz-flex;
  display: -webkit-flex;
  display: -ms-flex;
  display: flex;
  -moz-flex-direction: column;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -moz-justify-content: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
  background: #111;
  background: rgba(103, 109, 121, 0.8);
  left: 0;
  height: 100%;
  padding: 2em;
  position: fixed;
  text-align: center;
  top: 0;
  width: 100%;
  z-index: 10001;
  overflow-y: auto; }
  #dialog .modal {
    background: #fff;
    border-radius: 4px;
    width: 40em;
    padding: 2.75em 2em 0 2em;
    text-align: center;
    position: relative;
    max-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; }
    #dialog .modal:after {
      content: '';
      display: block;
      width: 100%;
      height: 1px;
      padding-bottom: 2.75em; }
    #dialog .modal .inner {
      display: -moz-flex;
      display: -webkit-flex;
      display: -ms-flex;
      display: flex;
      -moz-flex-direction: column;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -moz-align-items: center;
      -webkit-align-items: center;
      -ms-align-items: center;
      align-items: center;
      -moz-justify-content: center;
      -webkit-justify-content: center;
      -ms-justify-content: center;
      justify-content: center; }
    #dialog .modal .signup {
      margin-top: 1em; }
    #dialog .modal section {
      -moz-align-items: center;
      -webkit-align-items: center;
      -ms-align-items: center;
      align-items: center;
      display: -moz-flex;
      display: -webkit-flex;
      display: -ms-flex;
      display: flex;
      -moz-flex-direction: column;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      border-top: solid 1px #dee0e3;
      margin-top: 2em;
      padding-top: 2em; }
    #dialog .modal h2 {
      font-size: 2em;
      line-height: 1.35em;
      font-weight: 700;
      letter-spacing: -1.25px; }
    #dialog .modal h3 {
      font-size: 1.25em;
      line-height: 1.35em;
      font-weight: 700;
      letter-spacing: -0.5px;
      margin: 0 0 1em 0; }
    #dialog .modal p {
      line-height: 1.5em;
      font-size: 1.25em;
      margin: 0 0 1em 0;
      width: 100%; }
    #dialog .modal .actions {
      display: -moz-flex;
      display: -webkit-flex;
      display: -ms-flex;
      display: flex; }
    #dialog .modal .share {
      width: 13.75em;
      margin: 0 1em 0 0;
      height: 3em;
      line-height: 2.95em;
      vertical-align: middle; }
    #dialog .modal .button.twitter {
      width: 13.75em;
      padding-left: 0;
      padding-right: 0;
      margin: 0;
      height: 3em;
      line-height: 2.95em;
      vertical-align: middle; }
    #dialog .modal .closer {
      position: absolute;
      top: 0.75em;
      right: 0.75em;
      padding: 0;
      width: 2em;
      height: 2em;
      line-height: 2em;
      font-size: 1em;
      text-align: center;
      font-weight: 300;
      cursor: pointer;
      border-radius: 100%;
      -moz-transform: rotate(45deg);
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg); }
      #dialog .modal .closer:before {
        font-family: 'h5u';
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        content: '\e006'; }

/* Signup */
@-moz-keyframes input-error {
  0% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  25% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  50% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  75% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  100% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); } }

@-webkit-keyframes input-error {
  0% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  25% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  50% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  75% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  100% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); } }

@-ms-keyframes input-error {
  .signup 0% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  .signup 25% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  .signup 50% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  .signup 75% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  .signup 100% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); } }

@keyframes input-error {
  0% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  25% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  50% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); }
  75% {
    -moz-transform: translateX(0.35em);
    -webkit-transform: translateX(0.35em);
    -ms-transform: translateX(0.35em);
    transform: translateX(0.35em); }
  100% {
    -moz-transform: translateX(-0.35em);
    -webkit-transform: translateX(-0.35em);
    -ms-transform: translateX(-0.35em);
    transform: translateX(-0.35em); } }

.signup input {
  -moz-transition: opacity 0.5s ease;
  -webkit-transition: opacity 0.5s ease;
  -ms-transition: opacity 0.5s ease;
  transition: opacity 0.5s ease; }
  .signup input.error {
    -moz-animation: input-error 0.25s linear;
    -webkit-animation: input-error 0.25s linear;
    -ms-animation: input-error 0.25s linear;
    animation: input-error 0.25s linear; }

@-moz-keyframes button-loading {
  0% {
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-webkit-keyframes button-loading {
  0% {
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-ms-keyframes button-loading {
  .signup 0% {
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg); }
  .signup 100% {
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg); } }

@keyframes button-loading {
  0% {
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg); } }

.signup .button {
  position: relative;
  min-width: 7.5em; }
  .signup .button:before {
    -moz-animation: button-loading 1s linear infinite;
    -webkit-animation: button-loading 1s linear infinite;
    -ms-animation: button-loading 1s linear infinite;
    animation: button-loading 1s linear infinite;
    pointer-events: none;
    -moz-transition: opacity 0.5s ease;
    -webkit-transition: opacity 0.5s ease;
    -ms-transition: opacity 0.5s ease;
    transition: opacity 0.5s ease;
    -webkit-font-smoothing: antialiased;
    color: #ffffff;
    content: '';
    display: block;
    font-family: 'h5u';
    font-style: normal;
    font-variant: normal;
    font-weight: normal;
    height: 2em;
    left: 50%;
    line-height: 1;
    line-height: 2em;
    margin: -1em 0 0 -1em;
    opacity: 0;
    position: absolute;
    speak: none;
    text-transform: none;
    top: 50%;
    width: 2em; }

.signup.submitting input {
  pointer-events: none;
  opacity: 0.5; }

.signup.submitting .button {
  pointer-events: none;
  background-color: #ced0d3;
  color: transparent;
  border-color: transparent !important; }
  .signup.submitting .button:before {
    content: '\e003';
    opacity: 1; }

.signup.done .button {
  background-color: #21d2ac; }
  .signup.done .button:before {
    content: '\e007';
    -moz-animation: none !important;
    -webkit-animation: none !important;
    -ms-animation: none !important;
    animation: none !important; }

/* PX Banner */
#px-banner {
  margin-top: 2em;
  border-radius: 6px;
  background-image: url("images/px-bg.svg"), url("images/px-banner.jpg");
  background-position: center center, center center;
  background-size: auto, cover;
  background-attachment: scroll, fixed;
  color: #fff;
  padding: 4em;
  font-weight: 400; }
  #px-banner h2 {
    width: 25%;
    display: inline-block;
    background-image: url("images/px-logo.png");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    vertical-align: middle;
    height: 2em;
    text-indent: -9999px;
    position: relative;
    overflow-x: hidden; }
  #px-banner p {
    width: 50%;
    display: inline-block;
    vertical-align: middle;
    padding: 0 2em; }
  #px-banner .px-button {
    -moz-transition: background-color 0.15s ease-in-out;
    -webkit-transition: background-color 0.15s ease-in-out;
    -ms-transition: background-color 0.15s ease-in-out;
    transition: background-color 0.15s ease-in-out;
    width: 25%;
    display: inline-block;
    vertical-align: middle;
    font-size: 0.8em;
    letter-spacing: 0;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 600;
    cursor: default;
    background: #52cbee;
    border-radius: 4px;
    border: 0;
    color: white !important;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    border: 0;
    height: 3.25em;
    line-height: 3.25em;
    padding: 0 2em;
    white-space: nowrap; }
    #px-banner .px-button:hover {
      background: #2dc0ea; }

/* Wide */
@media screen and (max-width: 1280px) {
  /* Basic */
  body, input, textarea, select {
    font-size: 12pt; }
  /* Header */
  #header .features li {
    width: 8.5em; }
  /* Dialog */
  #dialog .modal {
    padding: 2.25em 1em 0 1em; }
    #dialog .modal:after {
      padding-bottom: 2.25em; } }

/* Normal */
@media screen and (max-width: 1140px) {
  /* Header */
  #header {
    text-align: center;
    margin: 5em 0 2em 0;
    padding-bottom: 7em; }
    #header .about {
      margin: 1em 0 1.5em 0;
      font-size: 2em; }
      #header .about br {
        display: none; }
    #header .features {
      position: relative; }
  /* Items */
  #items article header h2 {
    font-size: 1.75em; }
  #items article header .button {
    height: 2.75em;
    line-height: 2.7em;
    padding: 0 1em 0 1em; }
    #items article header .button span {
      display: none; }
  /* Footer */
  #footer {
    padding: 4em 0 4em 0; }
  /* Demo */
  #demo-header .left .selector, #demo-header .left .actions {
    display: none; }
  /* PX Banner */
  #px-banner {
    padding: 2em 3em;
    background-attachment: scroll; }
    #px-banner h2 {
      width: 20%; }
    #px-banner p {
      width: 60%; }
    #px-banner .px-button {
      width: 20%; } }

/* Narrow */
@media screen and (max-width: 960px) {
  /* Header */
  #header {
    margin: 4em 0 2em 0;
    padding-bottom: 6em; }
  /* Items */
  #items article {
    margin-top: 1.25em;
    border-radius: 4px !important;
    padding-bottom: 5em;
    background: #676D79; }
    #items article:first-child {
      margin-top: 0; }
    #items article .image {
      width: 153%;
      margin-top: -7%; }
    #items article header {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 5em;
      line-height: 5em;
      background: #fff;
      border: solid 1px #eef0f3;
      border-top: 0;
      border-radius: 0 0 4px 4px !important; }
      #items article header h2 {
        font-size: 1.5em;
        color: #6a707c;
        position: absolute;
        left: 1em;
        top: 0; }
      #items article header .actions {
        position: absolute;
        right: 0.75em;
        top: 0; }
  /* PX Banner */
  #px-banner {
    padding: 4em;
    text-align: center; }
    #px-banner h2 {
      width: 14em;
      display: block;
      margin: 0 auto 1em auto; }
    #px-banner p {
      width: 100%;
      padding: 0;
      margin: 0 auto 2em auto;
      max-width: 30em;
      display: block; }
    #px-banner .px-button {
      width: auto;
      min-width: 16em; } }

/* Narrower */
@media screen and (max-width: 768px) {
  /* Header */
  #header {
    margin: 4em 0 1.5em 0;
    padding-bottom: 4em; }
    #header .features li {
      width: 9em;
      margin: 0 0 2em 0; }
      #header .features li:after {
        display: none; }
      #header .features li:before {
        font-size: 44px; }
  /* Bar */
  #bar .notify > span {
    display: none; }
  /* Items */
  #items article {
    padding-bottom: 4em; }
    #items article header {
      height: 4em;
      line-height: 4em; }
      #items article header h2 {
        font-size: 1.25em;
        left: 1em; }
      #items article header .actions {
        right: 0.5em; }
      #items article header .button {
        margin-left: 0.5em; }
  /* Dialog */
  #dialog .modal {
    padding: 2.75em 2em 0 2em; }
    #dialog .modal:after {
      padding-bottom: 2.75em; }
    #dialog .modal p {
      font-size: 1em; } }

/* Mobile */
@media screen and (max-width: 736px) {
  /* Basic */
  body {
    min-width: 320px;
    font-size: 11.5pt; }
  input, textarea, select {
    font-size: 11.5pt; }
  ul.menu li {
    display: block;
    margin-top: 1em; }
    ul.menu li:first-child {
      margin-top: 0; }
    ul.menu li:before {
      display: none; }
  article.modal {
    padding: 2em 1em 2em 1em;
    font-size: 1em; }
  /* Header */
  #header {
    padding-bottom: 0;
    margin: 1.5em 0 2.5em 0; }
    #header .logo a {
      margin-bottom: 0;
      font-size: 1.75em; }
    #header .about {
      font-size: 1.75em;
      margin-bottom: 1em;
      line-height: 1.5em;
      letter-spacing: -0.75px; }
      #header .about span {
        display: none; }
    #header .features {
      margin: 0 0 2.5em 0;
      display: inline-block; }
      #header .features li {
        width: auto;
        display: block;
        margin: 1.25em auto 0 auto;
        letter-spacing: 0;
        font-size: 1em;
        text-align: left; }
        #header .features li br, #header .features li span {
          display: none; }
        #header .features li:first-child {
          margin-top: 0; }
        #header .features li:after {
          display: none; }
        #header .features li:before {
          display: inline-block;
          font-size: 24px;
          height: 1.75em;
          width: 1.75em;
          line-height: 1.75em;
          margin-right: 0.5em;
          position: relative;
          top: 0.15em; }
    #header.alt {
      margin: 2.5em 0 2.5em 0; }
      #header.alt h2 {
        font-size: 1.75em;
        margin-bottom: 1em;
        line-height: 1.5em;
        font-weight: 700;
        letter-spacing: -1px; }
  /* Bar */
  #bar {
    position: relative;
    height: auto;
    border-top: solid 1px #dee0e3;
    padding: 2em 0 0 0; }
    #bar .share {
      display: none; }
    #bar .notify {
      position: relative; }
      #bar .notify > span {
        display: block; }
      #bar .notify .button {
        margin: 1em 0 0 0; }
    #bar.docked {
      display: none; }
  /* Items */
  #items article {
    margin-top: 3%;
    padding-bottom: 3.5em; }
    #items article .image {
      position: relative;
      width: 220%;
      left: -28%; }
    #items article header {
      text-align: center;
      height: 3.5em;
      line-height: 3.5em; }
      #items article header h2 {
        letter-spacing: 0;
        font-size: 1.1em; }
      #items article header .actions .demo, #items article header .actions .download em {
        display: none; }
      #items article header .button {
        height: 2.5em;
        line-height: 2.5em;
        padding: 0 0.75em 0 0.75em; }
  /* Footer */
  #footer {
    padding: 2.5em 0 2.5em 0; }
    #footer .menu {
      padding: 0 2em 0 2em; }
    #footer .signup {
      margin: 1em auto;
      padding: 0 0.5em; }
    #footer .copyright {
      padding: 0 2em 0 2em; }
      #footer .copyright br {
        display: inline; }
  /* Demo */
  #demo-iframe-wrapper {
    min-width: 320px; }
  #demo-header {
    min-width: 320px; }
    #demo-header h1, #demo-header .share {
      display: none; }
    #demo-header .right {
      width: 100%;
      padding-left: 1em; }
    #demo-header .actions {
      width: 100%;
      margin: 0; }
      #demo-header .actions .download {
        position: absolute;
        right: 0;
        top: 0; }
  /* Dialog */
  #dialog {
    min-width: 320px;
    padding: 2em 0; }
    #dialog .modal {
      width: 100%;
      padding: 1.5em 1em 0 1em;
      border-radius: 0; }
      #dialog .modal:after {
        padding-bottom: 1.5em; }
      #dialog .modal .inner {
        width: 100%;
        max-width: 30em; }
      #dialog .modal section {
        margin-top: 1em;
        padding-top: 1em;
        width: 100%; }
      #dialog .modal .actions {
        -moz-flex-direction: column;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column; }
      #dialog .modal .share {
        margin-right: 0;
        margin-bottom: 1em; }
      #dialog .modal h2 {
        font-size: 1.25em;
        line-height: 1.5em;
        letter-spacing: -0.5px; }
        #dialog .modal h2 span {
          display: none; }
      #dialog .modal h3 {
        font-size: 1em;
        margin-bottom: 0.5em; }
      #dialog .modal p {
        font-size: 0.8em; }
      #dialog .modal .closer {
        top: 1.35em;
        right: 1em; }
  /* PX Banner */
  #px-banner {
    padding: 3em 2em; }
    #px-banner h2 {
      width: 10em; }
    #px-banner .px-button {
      display: block;
      width: 100%;
      max-width: 20em;
      margin: 0 auto; } }

/* Items */
@media screen and (min-width: 961px) {
  .button.demo:hover {
    color: inherit !important; }
  .button.download:hover {
    border-color: #e7746f !important;
    background: #e7746f !important;
    color: #fff !important; }
  article.item {
    color: #fff;
    background: #676D79; }
    article.item .button.demo {
      border-color: #fff;
      border-color: rgba(255, 255, 255, 0.5); }
      article.item .button.demo:hover {
        background: rgba(255, 255, 255, 0.1) !important; }
    article.item .button.download {
      border-color: #fff;
      background: #fff;
      color: #676D79; }
  article.paradigm-shift {
    color: #000000;
    background: #49fcd4; }
    article.paradigm-shift .button.demo {
      border-color: #000000; }
      article.paradigm-shift .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.paradigm-shift .button.download {
      border-color: #000000;
      background: #000000;
      color: #fff; }
  article.massively {
    background: #2E3339; }
    article.massively .button.download {
      color: #2E3339; }
  article.ethereal {
    background: #d57b7b; }
    article.ethereal .button.download {
      color: #d57b7b; }
  article.story {
    background: #2F363E; }
    article.story .button.download {
      color: #3D8089; }
  article.dimension {
    background: #18202D; }
    article.dimension .button.download {
      color: #18202D; }
  article.editorial {
    color: #3D4449;
    background: #F9F9F9; }
    article.editorial .button.demo {
      border-color: #C0C8CE; }
      article.editorial .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.editorial .button.download {
      border-color: #3D4449;
      background: #3D4449;
      color: #fff; }
  article.forty {
    background: #2C304B; }
    article.forty .button.download {
      color: #2C304B; }
  article.stellar {
    background: #7473AD; }
    article.stellar .button.download {
      color: #7473AD; }
  article.phantom {
    background: #D87D8F; }
    article.phantom .button.download {
      color: #D87D8F; }
      article.phantom .button.download:hover {
        background: #404145 !important;
        border-color: #404145 !important; }
  article.multiverse {
    background: #2E353D; }
    article.multiverse .button.download {
      color: #2E353D; }
  article.hyperspace {
    background: #3C1C5B; }
    article.hyperspace .button.download {
      color: #3C1C5B; }
  article.future-imperfect {
    background: #6B6F72; }
    article.future-imperfect .button.download {
      color: #6B6F72; }
  article.solid-state {
    background: #45568E; }
    article.solid-state .button.download {
      color: #45568E; }
  article.identity {
    color: #585C63;
    background: #D0D4DD; }
    article.identity .button.demo {
      border-color: #9EA2AA; }
      article.identity .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.identity .button.download {
      border-color: #585C63;
      background: #585C63;
      color: #fff; }
  article.lens {
    background: #9093A2; }
    article.lens .button.download {
      color: #9093A2; }
  article.fractal {
    background: #3483D2; }
    article.fractal .button.download {
      color: #3483D2; }
  article.eventually {
    background: #232A34; }
    article.eventually .button.download {
      color: #232A34; }
  article.spectral {
    background: #21AB9E; }
    article.spectral .button.download {
      color: #21AB9E; }
  article.photon {
    background: #5298A4; }
    article.photon .button.download {
      color: #5298A4; }
  article.highlights {
    background: #A5917A; }
    article.highlights .button.download {
      color: #A5917A; }
  article.landed {
    background: #568BB3; }
    article.landed .button.download {
      color: #568BB3; }
  article.strata {
    background: #837E7B; }
    article.strata .button.download {
      color: #837E7B; }
  article.read-only {
    color: #666;
    background: #EFEFEF; }
    article.read-only .button.demo {
      border-color: #bbb; }
      article.read-only .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.read-only .button.download {
      border-color: #666;
      background: #666;
      color: #fff; }
  article.alpha {
    background: #E4967F; }
    article.alpha .button.download {
      color: #E3957E; }
      article.alpha .button.download:hover {
        background: #2B2E37 !important;
        border-color: #2B2E37 !important; }
  article.directive {
    background: #4EB981; }
    article.directive .button.download {
      color: #4EB981; }
  article.aerial {
    background: #3C92B5; }
    article.aerial .button.download {
      color: #368EB2; }
  article.twenty {
    background: #6FC7BB; }
    article.twenty .button.download {
      color: #4fa79b; }
  article.big-picture {
    background: #687980; }
    article.big-picture .button.download {
      color: #6C8488; }
  article.tessellate {
    background: #875558; }
    article.tessellate .button.download {
      color: #875357; }
  article.overflow {
    background: #464D55; }
    article.overflow .button.download {
      color: #484C55; }
  article.prologue {
    background: #545B61; }
    article.prologue .button.download {
      color: #536066; }
  article.helios {
    color: #4C454D;
    background: #E4E4E6; }
    article.helios .button.demo {
      border-color: #aca8ad; }
      article.helios .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.helios .button.download {
      border-color: #4C454D;
      background: #4C454D;
      color: #fff; }
  article.telephasic {
    color: #52575c;
    background: #E9EDF0; }
    article.telephasic .button.demo {
      border-color: #c2c7cc; }
      article.telephasic .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.telephasic .button.download {
      border-color: #42474c;
      background: #42474c;
      color: #fff; }
  article.strongly-typed {
    color: #555;
    background: #EAEAEA; }
    article.strongly-typed .button.demo {
      border-color: #bbb; }
      article.strongly-typed .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.strongly-typed .button.download {
      border-color: #555;
      background: #555;
      color: #fff; }
  article.parallelism {
    background: #32323A; }
    article.parallelism .button.download {
      color: #35353D; }
  article.escape-velocity {
    background: #272D39; }
    article.escape-velocity .button.download {
      color: #414753; }
  article.astral {
    background: #38393E; }
    article.astral .button.download {
      color: #3C3D42; }
  article.striped {
    color: #252D38;
    background: #E9E9E9; }
    article.striped .button.demo {
      border-color: #959Da8; }
      article.striped .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.striped .button.download {
      border-color: #353D48;
      background: #353D48;
      color: #fff; }
  article.dopetrope {
    background: #342E30; }
    article.dopetrope .button.download {
      color: #352F31; }
  article.miniport {
    background: #2677AE; }
    article.miniport .button.download {
      color: #2174AA; }
  article.txt {
    color: #545B61;
    background: #E6E8E5; }
    article.txt .button.demo {
      border-color: #949ba1; }
      article.txt .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.txt .button.download {
      border-color: #545B61;
      background: #545B61;
      color: #fff; }
  article.verti {
    color: #444444;
    background: #E8E9EE; }
    article.verti .button.demo {
      border-color: #aaa; }
      article.verti .button.demo:hover {
        background: rgba(0, 0, 0, 0.05) !important; }
    article.verti .button.download {
      border-color: #444;
      background: #444;
      color: #fff; }
  article.zerofour {
    background: #3C8BB6; }
    article.zerofour .button.download {
      color: #3385B4; }
  article.arcana {
    background: #295C55; }
    article.arcana .button.download {
      color: #1C4849; }
  article.halcyonic {
    background: #293134; }
    article.halcyonic .button.download {
      color: #2B3034; }
  article.minimaxing {
    background: #007FAA; }
    article.minimaxing .button.download {
      color: #007BA7; } }
