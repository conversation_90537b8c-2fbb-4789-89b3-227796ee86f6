import os
import re
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

# Archivo HTML de entrada y salida
HTML_FILE = "pagina.html"
BASE_URL = "https://html5up.net"

# Carpetas destino
DEST_DIRS = {
    'css': 'assets/css/',
    'js': 'assets/js/',
    'icons': 'assets/icons/',
}

# Crear carpetas si no existen
def ensure_dirs():
    for d in DEST_DIRS.values():
        os.makedirs(d, exist_ok=True)

# Descargar un recurso y guardarlo localmente
def download_resource(url, dest):
    try:
        r = requests.get(url, timeout=10)
        if r.status_code == 200:
            with open(dest, 'wb') as f:
                f.write(r.content)
            print(f"Descargado: {url} -> {dest}")
        else:
            print(f"No se pudo descargar {url} (status {r.status_code})")
    except Exception as e:
        print(f"Error descargando {url}: {e}")

# Buscar y descargar recursos estáticos
def process_html():
    with open(HTML_FILE, encoding="utf-8") as f:
        html = f.read()

    # CSS
    html = re.sub(r'href="(/assets/css/([^"]+))"',
        lambda m: replace_and_download(m, 'css'), html)
    # JS
    html = re.sub(r'src="(/assets/js/([^"]+))"',
        lambda m: replace_and_download(m, 'js'), html)
    # Iconos
    html = re.sub(r'href="(/assets/icons/([^"]+))"',
        lambda m: replace_and_download(m, 'icons'), html)
    # Apple touch icon
    html = re.sub(r'href="(/assets/icons/([^"]+))"',
        lambda m: replace_and_download(m, 'icons'), html)

    # Iframe (descargar demo)
    html = re.sub(r'src="(/uploads/demos/aerial/)"',
        lambda m: replace_iframe(m), html)

    with open(HTML_FILE, "w", encoding="utf-8") as f:
        f.write(html)
    print("\nRecursos descargados y rutas actualizadas en pagina.html")

def replace_and_download(match, tipo):
    url_path = match.group(1)
    filename = match.group(2)
    url = urljoin(BASE_URL, url_path)
    dest = os.path.join(DEST_DIRS[tipo], filename)
    download_resource(url, dest)
    return f'{match.group(0)[:5]}{DEST_DIRS[tipo]}{filename}"'

def download_demo_recursive(demo_url, local_dir, visited=None):
    if visited is None:
        visited = set()
    if demo_url in visited:
        return
    visited.add(demo_url)
    try:
        r = requests.get(demo_url, timeout=10)
        if r.status_code == 200:
            # Guardar archivo
            parsed = urlparse(demo_url)
            path = parsed.path[len('/uploads/demos/aerial/'):]
            dest = os.path.join(local_dir, path)
            if demo_url.endswith('/') or dest.endswith(os.sep):
                dest = os.path.join(dest, 'index.html')
            os.makedirs(os.path.dirname(dest), exist_ok=True)
            with open(dest, 'wb') as f:
                f.write(r.content)
            print(f"Descargado demo: {demo_url} -> {dest}")
            # Si es HTML, buscar recursos
            if 'text/html' in r.headers.get('Content-Type', ''):
                soup = BeautifulSoup(r.text, 'html.parser')
                for tag in soup.find_all(['link', 'script', 'img']):
                    attr = 'src' if tag.name in ['script', 'img'] else 'href'
                    url = tag.get(attr)
                    if url and not url.startswith('data:'):
                        if url.startswith('http'):
                            resource_url = url
                        else:
                            resource_url = urljoin(demo_url, url)
                        if resource_url.startswith(BASE_URL + '/uploads/demos/aerial/'):
                            download_demo_recursive(resource_url, local_dir, visited)
        else:
            print(f"No se pudo descargar {demo_url} (status {r.status_code})")
    except Exception as e:
        print(f"Error descargando {demo_url}: {e}")

def replace_iframe(match):
    url_path = match.group(1)
    url = urljoin(BASE_URL, url_path)
    local_dir = "uploads/demos/aerial/"
    os.makedirs(local_dir, exist_ok=True)
    # Descargar recursivamente todo el demo
    download_demo_recursive(url, local_dir)
    return f'src="{local_dir}"'

if __name__ == "__main__":
    ensure_dirs()
    process_html()
