<!DOCTYPE html>
<html lang="en">
    <head> 
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title><PERSON> - Quantitative Developer | Data Scientist</title>
        <link rel="icon" type="image/x-icon" href="assets/favicon.ico" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css?family=Merriweather+Sans:400,700,800" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css?family=Merriweather:400,300,300italic,400italic,700,700italic" rel="stylesheet" type="text/css" />
        <link href="https://fonts.googleapis.com/css?family=Titillium+Web:400,600,700" rel="stylesheet" />
        <link href="https://cdnjs.cloudflare.com/ajax/libs/SimpleLightbox/2.1.0/simpleLightbox.min.css" rel="stylesheet" />
        <link href="css/styles.css" rel="stylesheet" />
        <style>
            /* --- Estilos para la Navbar y la Sección del Logo/Rol --- */
            #mainNav {
                background-color: rgba(0, 0, 0, 0.5); /* Un poco más de opacidad para mejor contraste */
                transition: background-color 0.3s ease; /* Transición suave para el fondo */
            }

            /* Quita el fondo oscuro cuando la barra se hace más pequeña al hacer scroll, si así lo tienes configurado en styles.css.
               Si no, asegúrate de que el script.js o la clase .navbar-scrolled de Bootstrap no lo sobrescriban. */
            #mainNav.navbar-scrolled {
                background-color: #fff; /* O el color que desees para el scroll, por ejemplo, blanco */
            }
            #mainNav.navbar-scrolled .navbar-brand .name,
            #mainNav.navbar-scrolled .navbar-brand .role,
            #mainNav.navbar-scrolled .navbar-nav .nav-link {
                color: #212529 !important; /* Texto oscuro cuando el fondo es claro */
                text-shadow: none; /* Elimina la sombra para fondos claros */
            }


            .navbar-brand {
                display: flex;
                flex-direction: column;
                align-items: flex-start; /* Alineado a la izquierda */
                padding: 0;
                margin-right: auto; /* Empuja la navegación a la derecha */
                line-height: 1.2; /* Ajuste para un espaciado más compacto entre líneas */
            }

            .navbar-brand .name {
                color: #d1f2fd; /* Un blanco roto suave */
                letter-spacing: 0.07em; /* Corregido: se cambió el ":" por ";" */
                font-size: 1.5rem; /* Reducido para mejor proporción con la navegación */
                font-weight: 700; /* Bold para impacto */
                text-shadow: 2px 2px 12px #021a23; /* Sombra sutil y definida */
                font-family: 'Titillium Web', Arial, sans-serif;
            }

            .navbar-brand .role {
                color: #c0c0c0; /* Gris claro que complementa el nombre */
                font-size: 0.95rem; /* Reducido para subcategoría */
                margin-top: 2px; /* Reducir la separación con el nombre */
                letter-spacing: 0.05em; /* Un poco de espaciado entre letras */
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4); /* Sombra más sutil */
                font-family: 'Merriweather Sans', sans-serif;
            }

            /* Asegurar que la navegación se vea bien */
            .navbar-nav .nav-link {
                color: #ffffff !important; /* Blanco puro para contraste */
                font-weight: 600; /* Menos bold que el nombre pero aún fuerte */
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4); /* Sombra sutil en los links */
                transition: color 0.3s ease;
                padding: 0.5rem 1rem; /* Aumentar el padding para que los enlaces no se vean tan "pegados" */
                font-size: 1.05rem; /* Ligeramente más grande para que coincida mejor con el nombre */
            }

            .navbar-nav .nav-link:hover {
                color: #f4623a !important; /* Color de acento de Bootstrap al hover */
            }

            /* --- Media Queries para Responsividad --- */
            @media (max-width: 991.98px) { /* Para pantallas de tablets y móviles */
                #mainNav {
                    background-color: rgba(0, 0, 0, 0.9); /* Más opaco para móviles */
                }
                .navbar-brand {
                    align-items: center; /* Centrar el texto en móviles */
                    width: 100%;
                }
                .navbar-brand .name {
                    font-size: 1.8rem; /* Reducir tamaño del nombre en móviles */
                }
                .navbar-brand .role {
                    font-size: 0.85rem; /* Reducir tamaño del rol en móviles */
                }
                .navbar-collapse {
                    background-color: rgba(0, 0, 0, 0.95); /* Fondo casi sólido para el menú colapsado */
                    padding: 15px;
                    border-radius: 5px; /* Bordes ligeramente redondeados */
                    margin-top: 10px; /* Separación del botón de colapso */
                }
                .navbar-nav .nav-link {
                    color: #ffffff !important;
                    text-align: center; /* Centrar los links del menú colapsado */
                    padding: 10px 0; /* Padding vertical para los links en el menú colapsado */
                    font-size: 1rem;
                }
            }

            /* Ajustes para el texto principal del masthead para que haya coherencia */
            .masthead h1 {
                font-size: 3rem; /* Ajuste del tamaño del H1 principal para que se vea más en sintonía */
                text-shadow: 2px 2px 10px rgba(0, 0, 0, 0.8); /* Una sombra para el H1 también */
            }
            .masthead p {
                font-size: 1.25rem; /* Ajuste del tamaño del párrafo */
                text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.6); /* Sombra para el párrafo */
            }
        </style>
    </head>
    <body id="page-top">
        <nav class="navbar navbar-expand-lg navbar-light fixed-top py-3" id="mainNav">
            <div class="container px-4 px-lg-5">
                <a class="navbar-brand" href="#page-top">
                    <div class="name">Jose Acosta</div>
                    <div class="role">Quantitative Developer | Data Scientist</div>
                </a>
                <button class="navbar-toggler navbar-toggler-right" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarResponsive">
                    <ul class="navbar-nav ms-auto my-2 my-lg-0">
                        <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                        <li class="nav-item"><a class="nav-link" href="#services">Services</a></li>
                        <li class="nav-item"><a class="nav-link" href="#portfolio">Portfolio</a></li>
                        <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                    </ul>
                </div>
            </div>
        </nav>
        <header class="masthead">
            <div class="container px-4 px-lg-5 h-100">
                <div class="row gx-4 gx-lg-5 h-100 align-items-center justify-content-center text-center">
                    <div class="col-lg-8 align-self-end">
                        <h1 class="text-white font-weight-bold">Your Favorite Place for Free Bootstrap Themes</h1>
                        <hr class="divider" />
                    </div>
                    <div class="col-lg-8 align-self-baseline">
                        <p class="text-white-75 mb-5">Start Bootstrap can help you build better websites using the Bootstrap framework! Just download a theme and start customizing, no strings attached!</p>
                        <a class="btn btn-primary btn-xl" href="#about">Find Out More</a>
                    </div>
                </div>
            </div>
        </header>
        <section class="page-section bg-primary" id="about">
            <div class="container px-4 px-lg-5">
                <div class="row gx-4 gx-lg-5 justify-content-center">
                    <div class="col-lg-8 text-center">
                        <h2 class="text-white mt-0">We've got what you need!</h2>
                        <hr class="divider divider-light" />
                        <p class="text-white-75 mb-4">Start Bootstrap has everything you need to get your new website up and running in no time! Choose one of our open source, free to download, and easy to use themes! No strings attached!</p>
                        <a class="btn btn-light btn-xl" href="#services">Get Started!</a>
                    </div>
                </div>
            </div>
        </section>
        <section class="page-section" id="services">
            <div class="container px-4 px-lg-5">
                <h2 class="text-center mt-0">At Your Service</h2>
                <hr class="divider" />
                <div class="row gx-4 gx-lg-5">
                    <div class="col-lg-3 col-md-6 text-center">
                        <div class="mt-5">
                            <div class="mb-2"><i class="bi-gem fs-1 text-primary"></i></div>
                            <h3 class="h4 mb-2">Sturdy Themes</h3>
                            <p class="text-muted mb-0">Our themes are updated regularly to keep them bug free!</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 text-center">
                        <div class="mt-5">
                            <div class="mb-2"><i class="bi-laptop fs-1 text-primary"></i></div>
                            <h3 class="h4 mb-2">Up to Date</h3>
                            <p class="text-muted mb-0">All dependencies are kept current to keep things fresh.</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 text-center">
                        <div class="mt-5">
                            <div class="mb-2"><i class="bi-globe fs-1 text-primary"></i></div>
                            <h3 class="h4 mb-2">Ready to Publish</h3>
                            <p class="text-muted mb-0">You can use this design as is, or you can make changes!</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 text-center">
                        <div class="mt-5">
                            <div class="mb-2"><i class="bi-heart fs-1 text-primary"></i></div>
                            <h3 class="h4 mb-2">Made with Love</h3>
                            <p class="text-muted mb-0">Is it really open source if it's not made with love?</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div id="portfolio">
            <div class="container-fluid p-0">
                <div class="row g-0">
                    <div class="col-lg-4 col-sm-6">
                        <a class="portfolio-box" href="assets/img/portfolio/fullsize/1.jpg" title="Project Name">
                            <img class="img-fluid" src="assets/img/portfolio/thumbnails/1.jpg" alt="..." />
                            <div class="portfolio-box-caption">
                                <div class="project-category text-white-50">Category</div>
                                <div class="project-name">Project Name</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-4 col-sm-6">
                        <a class="portfolio-box" href="assets/img/portfolio/fullsize/2.jpg" title="Project Name">
                            <img class="img-fluid" src="assets/img/portfolio/thumbnails/2.jpg" alt="..." />
                            <div class="portfolio-box-caption">
                                <div class="project-category text-white-50">Category</div>
                                <div class="project-name">Project Name</div>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-4 col-sm-6">
                        <a class="portfolio-box" href="assets/img/portfolio/fullsize/3.jpg" title="Project Name">
                            <img class="img-fluid" src="assets/img/portfolio/thumbnails/3.jpg" alt="..." />
                            <div class="portfolio-box-caption">
                                <div class="project-category text-white-50">Category</div>
                                <div class="project-name">Project Name</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <section class="page-section bg-dark text-white">
            <div class="container px-4 px-lg-5 text-center">
                <h2 class="mb-4">Free Download at Start Bootstrap!</h2>
                <a class="btn btn-light btn-xl" href="https://startbootstrap.com/theme/creative/">Download Now!</a>
            </div>
        </section>
        <section class="page-section" id="contact">
            <div class="container px-4 px-lg-5">
                <div class="row gx-4 gx-lg-5 justify-content-center">
                    <div class="col-lg-8 col-xl-6 text-center">
                        <h2 class="mt-0">Let's Get In Touch!</h2>
                        <hr class="divider" />
                        <p class="text-muted mb-5">Ready to start your next project with us? Send us a message and we will get back to you as soon as possible!</p>
                    </div>
                </div>
                <div class="row gx-4 gx-lg-5 justify-content-center mb-5">
                    <div class="col-lg-6">
                        <form id="contactForm" data-sb-form-api-token="API_TOKEN">
                            <div class="form-floating mb-3">
                                <input class="form-control" id="name" type="text" placeholder="Enter your name..." data-sb-validations="required" />
                                <label for="name">Full name</label>
                                <div class="invalid-feedback" data-sb-feedback="name:required">A name is required.</div>
                            </div>
                            <div class="form-floating mb-3">
                                <input class="form-control" id="email" type="email" placeholder="<EMAIL>" data-sb-validations="required,email" />
                                <label for="email">Email address</label>
                                <div class="invalid-feedback" data-sb-feedback="email:required">An email is required.</div>
                                <div class="invalid-feedback" data-sb-feedback="email:email">Email is not valid.</div>
                            </div>
                            <div class="form-floating mb-3">
                                <input class="form-control" id="phone" type="tel" placeholder="(*************" data-sb-validations="required" />
                                <label for="phone">Phone number</label>
                                <div class="invalid-feedback" data-sb-feedback="phone:required">A phone number is required.</div>
                            </div>
                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="message" type="text" placeholder="Enter your message here..." style="height: 10rem" data-sb-validations="required"></textarea>
                                <label for="message">Message</label>
                                <div class="invalid-feedback" data-sb-feedback="message:required">A message is required.</div>
                            </div>
                            <div class="d-none" id="submitSuccessMessage">
                                <div class="text-center mb-3">
                                    <div class="fw-bolder">Form submission successful!</div>
                                    To activate this form, sign up at
                                    <br />
                                    <a href="https://startbootstrap.com/solution/contact-forms">https://startbootstrap.com/solution/contact-forms</a>
                                </div>
                            </div>
                            <div class="d-none" id="submitErrorMessage">
                                <div class="text-center text-danger mb-3">Error sending message!</div>
                            </div>
                            <div class="d-grid">
                                <button class="btn btn-primary btn-xl disabled" id="submitButton" type="submit">Submit</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
        <footer class="bg-light py-5">
            <div class="container px-4 px-lg-5">
                <div class="small text-center text-muted">Copyright &copy; 2023 - Company Name</div>
            </div>
        </footer>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/SimpleLightbox/2.1.0/simpleLightbox.min.js"></script>
        <script src="js/scripts.js"></script>
    </body>
</html>
